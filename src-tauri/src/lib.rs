use tauri_plugin_sql::{Migration, MigrationKind};
use serde::{Deserialize, Serialize};
use tauri::State;
use std::sync::Mutex;
use std::collections::HashMap;

// Data structures for the POS system
#[derive(Debug, Serialize, Deserialize)]
pub struct Product {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
    pub price: f64,
    pub category_id: Option<i64>,
    pub vat_rate_id: i64,
    pub barcode: Option<String>,
    pub color: String,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Category {
    pub id: Option<i64>,
    pub name: String,
    pub color: String,
    pub icon: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CartItem {
    pub product_id: i64,
    pub name: String,
    pub price: f64,
    pub quantity: i32,
    pub vat_rate: f64,
    pub color: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Transaction {
    pub id: Option<i64>,
    pub transaction_number: String,
    pub subtotal: f64,
    pub vat_amount: f64,
    pub total: f64,
    pub items: Vec<CartItem>,
    pub payment_method: String,
}

// Application state
#[derive(Debug, Default)]
pub struct AppState {
    pub current_cart: Vec<CartItem>,
    pub till_session_id: Option<i64>,
}

// POS Commands
#[tauri::command]
async fn get_products() -> Result<Vec<Product>, String> {
    // For now, return sample data until we can properly connect to the database
    // This will be replaced with actual database queries once the frontend is working
    let sample_products = vec![
        Product {
            id: Some(1),
            name: "Coffee".to_string(),
            description: Some("Premium coffee blend".to_string()),
            price: 2.50,
            category_id: Some(1),
            vat_rate_id: 1,
            barcode: Some("123456789".to_string()),
            color: "#8B4513".to_string(),
            is_active: true,
        },
        Product {
            id: Some(2),
            name: "Tea".to_string(),
            description: Some("English breakfast tea".to_string()),
            price: 2.00,
            category_id: Some(1),
            vat_rate_id: 1,
            barcode: Some("987654321".to_string()),
            color: "#228B22".to_string(),
            is_active: true,
        },
        Product {
            id: Some(3),
            name: "Sandwich".to_string(),
            description: Some("Fresh sandwich".to_string()),
            price: 4.50,
            category_id: Some(2),
            vat_rate_id: 1,
            barcode: Some("456789123".to_string()),
            color: "#DAA520".to_string(),
            is_active: true,
        },
    ];

    Ok(sample_products)
}

#[tauri::command]
async fn get_categories() -> Result<Vec<Category>, String> {
    // Sample categories for now
    let sample_categories = vec![
        Category {
            id: Some(1),
            name: "Beverages".to_string(),
            color: "#3B82F6".to_string(),
            icon: Some("☕".to_string()),
        },
        Category {
            id: Some(2),
            name: "Food".to_string(),
            color: "#10B981".to_string(),
            icon: Some("🍽️".to_string()),
        },
        Category {
            id: Some(3),
            name: "Snacks".to_string(),
            color: "#F59E0B".to_string(),
            icon: Some("🍿".to_string()),
        },
    ];

    Ok(sample_categories)
}

#[tauri::command]
async fn add_to_cart(
    product_id: i64,
    quantity: i32,
    state: State<'_, Mutex<AppState>>,
) -> Result<Vec<CartItem>, String> {
    // Get sample products for now
    let products = get_products().await?;

    let product = products.iter().find(|p| p.id == Some(product_id))
        .ok_or("Product not found")?;

    // Update cart state
    let mut app_state = state.lock().unwrap();

    // Check if item already exists in cart
    if let Some(existing_item) = app_state.current_cart.iter_mut().find(|item| item.product_id == product_id) {
        existing_item.quantity += quantity;
    } else {
        app_state.current_cart.push(CartItem {
            product_id,
            name: product.name.clone(),
            price: product.price,
            quantity,
            vat_rate: 0.2, // 20% VAT for now
            color: product.color.clone(),
        });
    }

    Ok(app_state.current_cart.clone())
}

#[tauri::command]
async fn get_cart(state: State<'_, Mutex<AppState>>) -> Result<Vec<CartItem>, String> {
    let app_state = state.lock().unwrap();
    Ok(app_state.current_cart.clone())
}

#[tauri::command]
async fn clear_cart(state: State<'_, Mutex<AppState>>) -> Result<(), String> {
    let mut app_state = state.lock().unwrap();
    app_state.current_cart.clear();
    Ok(())
}

#[tauri::command]
async fn remove_from_cart(
    product_id: i64,
    state: State<'_, Mutex<AppState>>,
) -> Result<Vec<CartItem>, String> {
    let mut app_state = state.lock().unwrap();
    app_state.current_cart.retain(|item| item.product_id != product_id);
    Ok(app_state.current_cart.clone())
}

#[tauri::command]
async fn process_transaction(
    payment_method: String,
    state: State<'_, Mutex<AppState>>,
) -> Result<Transaction, String> {
    let mut app_state = state.lock().unwrap();

    if app_state.current_cart.is_empty() {
        return Err("Cart is empty".to_string());
    }

    // Calculate totals
    let mut subtotal = 0.0;
    let mut vat_amount = 0.0;

    for item in &app_state.current_cart {
        let line_total = item.price * item.quantity as f64;
        let line_vat = line_total * item.vat_rate / (1.0 + item.vat_rate);
        subtotal += line_total - line_vat;
        vat_amount += line_vat;
    }

    let total = subtotal + vat_amount;
    let transaction_number = format!("TXN{}", std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs());

    let transaction = Transaction {
        id: None,
        transaction_number: transaction_number.clone(),
        subtotal,
        vat_amount,
        total,
        items: app_state.current_cart.clone(),
        payment_method,
    };

    // Clear cart after successful transaction
    app_state.current_cart.clear();

    Ok(transaction)
}

// Database viewer commands
#[tauri::command]
async fn get_database_tables() -> Result<Vec<String>, String> {
    // Return the main tables in our POS system
    let tables = vec![
        "products".to_string(),
        "categories".to_string(),
        "transactions".to_string(),
        "transaction_items".to_string(),
        "vat_rates".to_string(),
    ];
    Ok(tables)
}

#[tauri::command]
async fn get_table_data(table_name: String) -> Result<Vec<HashMap<String, serde_json::Value>>, String> {
    // For now, return sample data based on table name
    // In a real implementation, this would query the actual database
    match table_name.as_str() {
        "products" => {
            let products = get_products().await?;
            let mut result = Vec::new();
            for product in products {
                let mut row = HashMap::new();
                row.insert("id".to_string(), serde_json::Value::Number(serde_json::Number::from(product.id.unwrap_or(0))));
                row.insert("name".to_string(), serde_json::Value::String(product.name));
                row.insert("description".to_string(), serde_json::Value::String(product.description.unwrap_or("".to_string())));
                row.insert("price".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(product.price).unwrap()));
                row.insert("category_id".to_string(), serde_json::Value::Number(serde_json::Number::from(product.category_id.unwrap_or(0))));
                row.insert("barcode".to_string(), serde_json::Value::String(product.barcode.unwrap_or("".to_string())));
                row.insert("color".to_string(), serde_json::Value::String(product.color));
                row.insert("is_active".to_string(), serde_json::Value::Bool(product.is_active));
                result.push(row);
            }
            Ok(result)
        },
        "categories" => {
            let categories = get_categories().await?;
            let mut result = Vec::new();
            for category in categories {
                let mut row = HashMap::new();
                row.insert("id".to_string(), serde_json::Value::Number(serde_json::Number::from(category.id.unwrap_or(0))));
                row.insert("name".to_string(), serde_json::Value::String(category.name));
                row.insert("color".to_string(), serde_json::Value::String(category.color));
                row.insert("icon".to_string(), serde_json::Value::String(category.icon.unwrap_or("📦".to_string())));
                result.push(row);
            }
            Ok(result)
        },
        "transactions" => {
            // Sample transaction data
            let mut result = Vec::new();
            let mut row = HashMap::new();
            row.insert("id".to_string(), serde_json::Value::Number(serde_json::Number::from(1)));
            row.insert("transaction_number".to_string(), serde_json::Value::String("TXN001".to_string()));
            row.insert("subtotal".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(10.00).unwrap()));
            row.insert("vat_amount".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(2.00).unwrap()));
            row.insert("total".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(12.00).unwrap()));
            row.insert("payment_method".to_string(), serde_json::Value::String("cash".to_string()));
            row.insert("created_at".to_string(), serde_json::Value::String("2024-01-15 10:30:00".to_string()));
            result.push(row);
            Ok(result)
        },
        _ => {
            Err(format!("Table '{}' not found", table_name))
        }
    }
}

// Product management commands
#[tauri::command]
async fn get_all_products() -> Result<Vec<Product>, String> {
    get_products().await
}

#[tauri::command]
async fn get_all_categories() -> Result<Vec<Category>, String> {
    get_categories().await
}

#[tauri::command]
async fn create_product(product: Product) -> Result<Product, String> {
    // For now, just return the product with a generated ID
    // In a real implementation, this would insert into the database
    let mut new_product = product;
    new_product.id = Some(999); // Mock ID
    Ok(new_product)
}

#[tauri::command]
async fn update_product(id: i64, product: Product) -> Result<Product, String> {
    // For now, just return the updated product
    // In a real implementation, this would update the database
    let mut updated_product = product;
    updated_product.id = Some(id);
    Ok(updated_product)
}

#[tauri::command]
async fn delete_product(id: i64) -> Result<(), String> {
    // For now, just return success
    // In a real implementation, this would delete from the database
    println!("Deleting product with ID: {}", id);
    Ok(())
}

#[tauri::command]
async fn create_category(category: Category) -> Result<Category, String> {
    // For now, just return the category with a generated ID
    // In a real implementation, this would insert into the database
    let mut new_category = category;
    new_category.id = Some(999); // Mock ID
    Ok(new_category)
}

#[tauri::command]
async fn update_category(id: i64, category: Category) -> Result<Category, String> {
    // For now, just return the updated category
    // In a real implementation, this would update the database
    let mut updated_category = category;
    updated_category.id = Some(id);
    Ok(updated_category)
}

#[tauri::command]
async fn delete_category(id: i64) -> Result<(), String> {
    // For now, just return success
    // In a real implementation, this would delete from the database
    println!("Deleting category with ID: {}", id);
    Ok(())
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let migrations = vec![
        Migration {
            version: 1,
            description: "create_initial_tables",
            sql: include_str!("../migrations/001_initial.sql"),
            kind: MigrationKind::Up,
        }
    ];

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(
            tauri_plugin_sql::Builder::default()
                .add_migrations("sqlite:pos.db", migrations)
                .build(),
        )
        .manage(Mutex::new(AppState::default()))
        .invoke_handler(tauri::generate_handler![
            greet,
            get_products,
            get_categories,
            add_to_cart,
            get_cart,
            clear_cart,
            remove_from_cart,
            process_transaction,
            get_database_tables,
            get_table_data,
            get_all_products,
            get_all_categories,
            create_product,
            update_product,
            delete_product,
            create_category,
            update_category,
            delete_category
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
