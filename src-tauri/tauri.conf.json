{"$schema": "https://schema.tauri.app/config/2", "productName": "pos", "version": "0.1.0", "identifier": "com.pos.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../build"}, "app": {"windows": [{"title": "UK Point of Sale System", "width": 1400, "height": 900, "minWidth": 1200, "minHeight": 800, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "plugins": {"sql": {"preload": ["sqlite:pos.db"]}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}