<script lang="ts">
  import { onMount } from "svelte";

  // <PERSON><PERSON> invoke function for web development
  async function invoke(command: string, args?: any): Promise<any> {
    console.log(`Mock invoke: ${command}`, args);

    // Mock responses for different commands
    switch (command) {
      case "get_all_products":
        return [
          { id: 1, name: "Coffee", price: 2.50, vat_rate: 0.2, color: "#8B4513", barcode: "123456789", description: "Premium coffee blend" },
          { id: 2, name: "Tea", price: 2.00, vat_rate: 0.2, color: "#228B22", barcode: "987654321", description: "English breakfast tea" },
          { id: 3, name: "Sandwich", price: 4.50, vat_rate: 0.2, color: "#DAA520", barcode: "456789123", description: "Fresh sandwich" }
        ];
      case "get_all_categories":
        return [
          { id: 1, name: "Beverages", color: "#3B82F6", icon: "☕" },
          { id: 2, name: "Food", color: "#10B981", icon: "🍽️" },
          { id: 3, name: "Snacks", color: "#F59E0B", icon: "🍿" }
        ];
      case "get_database_tables":
        return ["products", "categories", "transactions", "transaction_items", "vat_rates"];
      case "get_table_data":
        if (args?.tableName === "products") {
          return [
            { id: 1, name: "Coffee", price: 2.50, barcode: "123456789", color: "#8B4513", is_active: true },
            { id: 2, name: "Tea", price: 2.00, barcode: "987654321", color: "#228B22", is_active: true },
            { id: 3, name: "Sandwich", price: 4.50, barcode: "456789123", color: "#DAA520", is_active: true }
          ];
        } else if (args?.tableName === "categories") {
          return [
            { id: 1, name: "Beverages", color: "#3B82F6", icon: "☕" },
            { id: 2, name: "Food", color: "#10B981", icon: "🍽️" },
            { id: 3, name: "Snacks", color: "#F59E0B", icon: "🍿" }
          ];
        } else if (args?.tableName === "transactions") {
          return [
            { id: 1, transaction_number: "TXN001", subtotal: 10.00, vat_amount: 2.00, total: 12.00, payment_method: "cash", created_at: "2024-01-15 10:30:00" }
          ];
        }
        return [];
      case "create_product":
      case "update_product":
        return { ...args.product, id: args.id || 999 };
      case "create_category":
      case "update_category":
        return { ...args.category, id: args.id || 999 };
      case "delete_product":
      case "delete_category":
        return null;
      default:
        return null;
    }
  }

  // Types
  interface Product {
    id?: number;
    name: string;
    description?: string;
    price: number;
    barcode?: string;
    category_id?: number;
    vat_rate: number;
    color: string;
    created_at?: string;
    updated_at?: string;
  }

  interface Category {
    id?: number;
    name: string;
    description?: string;
    color: string;
    icon?: string;
    created_at?: string;
    updated_at?: string;
  }

  // Dashboard state
  let currentTime = $state(new Date());
  let businessStats = $state({
    todaySales: 1247.50,
    todayTransactions: 89,
    tillStatus: 'open',
    lastBackup: '2024-01-15 09:30'
  });

  // Product Management State
  let showProductModal = $state(false);
  let showCategoryModal = $state(false);
  let showProductList = $state(false);
  let showCategoryList = $state(false);
  let showVirtualKeyboard = $state(false);
  let showDatabaseViewer = $state(false);

  let products = $state<Product[]>([]);
  let categories = $state<Category[]>([]);
  let filteredProducts = $state<Product[]>([]);
  let selectedCategory = $state<number | null>(null);

  let editingProduct = $state<Product | null>(null);
  let editingCategory = $state<Category | null>(null);

  let newProduct = $state<Product>({
    name: '',
    description: '',
    price: 0,
    barcode: '',
    category_id: undefined,
    vat_rate: 0.2,
    color: '#3b82f6'
  });

  let newCategory = $state<Category>({
    name: '',
    description: '',
    color: '#3b82f6',
    icon: '📦'
  });

  // Virtual Keyboard State
  let activeInput = $state<HTMLInputElement | null>(null);
  let keyboardValue = $state('');

  // Database viewer state
  let databaseTables = $state<string[]>([]);
  let selectedTable = $state<string>('');
  let tableData = $state<any[]>([]);

  // Update time every second
  onMount(() => {
    const interval = setInterval(() => {
      currentTime = new Date();
    }, 1000);

    loadProducts();
    loadCategories();
    loadDatabaseTables();

    return () => clearInterval(interval);
  });

  // Load data functions
  async function loadProducts() {
    try {
      const result = await invoke("get_all_products");
      products = Array.isArray(result) ? result : [];
      filterProducts();
    } catch (error) {
      console.error("Failed to load products:", error);
      // Fallback sample data
      products = [
        { id: 1, name: "Sample Product 1", price: 9.99, vat_rate: 0.2, color: "#3b82f6", barcode: "1234567890" },
        { id: 2, name: "Sample Product 2", price: 19.99, vat_rate: 0.2, color: "#10b981", barcode: "0987654321" }
      ];
      filterProducts();
    }
  }

  async function loadCategories() {
    try {
      const result = await invoke("get_all_categories");
      categories = Array.isArray(result) ? result : [];
    } catch (error) {
      console.error("Failed to load categories:", error);
      // Fallback sample data
      categories = [
        { id: 1, name: "Food & Drink", color: "#10b981", icon: "🍕" },
        { id: 2, name: "Electronics", color: "#3b82f6", icon: "📱" }
      ];
    }
  }

  async function loadDatabaseTables() {
    try {
      const result = await invoke("get_database_tables");
      databaseTables = Array.isArray(result) ? result : ['products', 'categories', 'transactions'];
    } catch (error) {
      console.error("Failed to load database tables:", error);
      databaseTables = ['products', 'categories', 'transactions'];
    }
  }

  function filterProducts() {
    if (selectedCategory === null) {
      filteredProducts = products;
    } else {
      filteredProducts = products.filter(p => p.category_id === selectedCategory);
    }
  }

  function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  }

  function formatTime(date: Date): string {
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  function formatDate(date: Date): string {
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Product Management Functions
  function openProductModal(product?: Product) {
    if (product) {
      editingProduct = product;
      newProduct = { ...product };
    } else {
      editingProduct = null;
      newProduct = {
        name: '',
        description: '',
        price: 0,
        barcode: '',
        category_id: undefined,
        vat_rate: 0.2,
        color: '#3b82f6'
      };
    }
    showProductModal = true;
  }

  function openCategoryModal(category?: Category) {
    if (category) {
      editingCategory = category;
      newCategory = { ...category };
    } else {
      editingCategory = null;
      newCategory = {
        name: '',
        description: '',
        color: '#3b82f6',
        icon: '📦'
      };
    }
    showCategoryModal = true;
  }

  async function saveProduct() {
    try {
      if (editingProduct) {
        await invoke("update_product", { id: editingProduct.id, product: newProduct });
      } else {
        await invoke("create_product", newProduct);
      }
      await loadProducts();
      showProductModal = false;
    } catch (error) {
      console.error("Failed to save product:", error);
      alert("Failed to save product. Please try again.");
    }
  }

  async function saveCategory() {
    try {
      if (editingCategory) {
        await invoke("update_category", { id: editingCategory.id, category: newCategory });
      } else {
        await invoke("create_category", newCategory);
      }
      await loadCategories();
      showCategoryModal = false;
    } catch (error) {
      console.error("Failed to save category:", error);
      alert("Failed to save category. Please try again.");
    }
  }

  async function deleteProduct(id: number) {
    if (confirm("Are you sure you want to delete this product?")) {
      try {
        await invoke("delete_product", { id });
        await loadProducts();
      } catch (error) {
        console.error("Failed to delete product:", error);
        alert("Failed to delete product. Please try again.");
      }
    }
  }

  async function deleteCategory(id: number) {
    if (confirm("Are you sure you want to delete this category?")) {
      try {
        await invoke("delete_category", { id });
        await loadCategories();
      } catch (error) {
        console.error("Failed to delete category:", error);
        alert("Failed to delete category. Please try again.");
      }
    }
  }

  // Virtual Keyboard Functions
  function openVirtualKeyboard(input: HTMLInputElement) {
    activeInput = input;
    keyboardValue = input.value;
    showVirtualKeyboard = true;
  }

  function typeKey(key: string) {
    if (key === 'Backspace') {
      keyboardValue = keyboardValue.slice(0, -1);
    } else if (key === 'Space') {
      keyboardValue += ' ';
    } else {
      keyboardValue += key;
    }

    if (activeInput) {
      activeInput.value = keyboardValue;
      activeInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
  }

  function applyKeyboardInput() {
    if (activeInput) {
      activeInput.value = keyboardValue;
      activeInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
    showVirtualKeyboard = false;
    activeInput = null;
  }

  // Database Viewer Functions
  async function loadTableData(tableName: string) {
    try {
      const result = await invoke("get_table_data", { tableName });
      tableData = Array.isArray(result) ? result : [];
      selectedTable = tableName;
    } catch (error) {
      console.error("Failed to load table data:", error);
      tableData = [];
    }
  }

  // Detect if device has physical keyboard
  function hasPhysicalKeyboard(): boolean {
    return window.navigator.maxTouchPoints === 0 || window.innerWidth > 768;
  }
</script>

<!-- Management Dashboard -->
<div class="dashboard-container">
  <!-- Header -->
  <header class="dashboard-header">
    <div class="header-left">
      <h1 class="dashboard-title">🏪 UK POS Management Dashboard</h1>
      <div class="business-info">
        <span class="business-name">Your Business Name</span>
        <span class="business-location">📍 London, UK</span>
      </div>
    </div>
    <div class="header-right">
      <div class="current-time">
        <div class="time">{formatTime(currentTime)}</div>
        <div class="date">{formatDate(currentTime)}</div>
      </div>
      <div class="till-status">
        <span class="status-indicator {businessStats.tillStatus}"></span>
        <span>Till {businessStats.tillStatus === 'open' ? 'Open' : 'Closed'}</span>
      </div>
    </div>
  </header>

  <!-- Quick Stats Bar -->
  <div class="stats-bar">
    <div class="stat-card">
      <div class="stat-icon">💰</div>
      <div class="stat-content">
        <div class="stat-value">{formatCurrency(businessStats.todaySales)}</div>
        <div class="stat-label">Today's Sales</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">🧾</div>
      <div class="stat-content">
        <div class="stat-value">{businessStats.todayTransactions}</div>
        <div class="stat-label">Transactions</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">💾</div>
      <div class="stat-content">
        <div class="stat-value">{businessStats.lastBackup}</div>
        <div class="stat-label">Last Backup</div>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Grid -->
  <div class="dashboard-grid">
    <!-- POS Operations -->
    <div class="tile-section">
      <h2 class="section-title">🛒 POS Operations</h2>
      <div class="tiles-grid">
        <a href="/pos" class="dashboard-tile primary">
          <div class="tile-icon">🏪</div>
          <div class="tile-content">
            <h3>Point of Sale</h3>
            <p>Process transactions and manage sales</p>
          </div>
          <div class="tile-arrow">→</div>
        </a>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">💷</div>
          <div class="tile-content">
            <h3>Open Till</h3>
            <p>Start a new till session</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">🔒</div>
          <div class="tile-content">
            <h3>Close Till</h3>
            <p>End current session & reconcile</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">🚨</div>
          <div class="tile-content">
            <h3>Emergency</h3>
            <p>Emergency functions & void</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Product Management -->
    <div class="tile-section">
      <h2 class="section-title">📦 Product Management</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile primary" onclick={() => showProductList = true}>
          <div class="tile-icon">🛍️</div>
          <div class="tile-content">
            <h3>Manage Products</h3>
            <p>Add, edit, delete products</p>
          </div>
        </button>

        <button class="dashboard-tile primary" onclick={() => showCategoryList = true}>
          <div class="tile-icon">📂</div>
          <div class="tile-content">
            <h3>Categories</h3>
            <p>Organize product categories</p>
          </div>
        </button>

        <button class="dashboard-tile primary" onclick={() => openProductModal()}>
          <div class="tile-icon">➕</div>
          <div class="tile-content">
            <h3>Add Product</h3>
            <p>Create new product</p>
          </div>
        </button>

        <button class="dashboard-tile primary" onclick={() => openCategoryModal()}>
          <div class="tile-icon">📁</div>
          <div class="tile-content">
            <h3>Add Category</h3>
            <p>Create new category</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Store Management -->
    <div class="tile-section">
      <h2 class="section-title">🏬 Store Management</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile secondary">
          <div class="tile-icon">⚙️</div>
          <div class="tile-content">
            <h3>Settings</h3>
            <p>Business details & preferences</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">💳</div>
          <div class="tile-content">
            <h3>Payment Methods</h3>
            <p>Configure payment options</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">🕒</div>
          <div class="tile-content">
            <h3>Business Hours</h3>
            <p>Set opening times</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">📍</div>
          <div class="tile-content">
            <h3>Location Settings</h3>
            <p>Store location & details</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Reports & Analytics -->
    <div class="tile-section">
      <h2 class="section-title">📊 Reports & Analytics</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile accent">
          <div class="tile-icon">📈</div>
          <div class="tile-content">
            <h3>Sales Reports</h3>
            <p>Daily, weekly, monthly sales</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">🧾</div>
          <div class="tile-content">
            <h3>Transaction History</h3>
            <p>View all transactions</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">🏛️</div>
          <div class="tile-content">
            <h3>VAT Reports</h3>
            <p>UK VAT compliance reports</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">📋</div>
          <div class="tile-content">
            <h3>Summary Reports</h3>
            <p>Business performance overview</p>
          </div>
        </button>
      </div>
    </div>

    <!-- User Management -->
    <div class="tile-section">
      <h2 class="section-title">👥 User Management</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile warning">
          <div class="tile-icon">👤</div>
          <div class="tile-content">
            <h3>Staff Accounts</h3>
            <p>Manage user accounts</p>
          </div>
        </button>

        <button class="dashboard-tile warning">
          <div class="tile-icon">🔐</div>
          <div class="tile-content">
            <h3>Permissions</h3>
            <p>Set access levels</p>
          </div>
        </button>

        <button class="dashboard-tile warning">
          <div class="tile-icon">🔑</div>
          <div class="tile-content">
            <h3>Access Control</h3>
            <p>Security settings</p>
          </div>
        </button>

        <button class="dashboard-tile warning">
          <div class="tile-icon">📝</div>
          <div class="tile-content">
            <h3>Activity Log</h3>
            <p>User activity tracking</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Promotions & Offers -->
    <div class="tile-section">
      <h2 class="section-title">🎯 Promotions & Offers</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile success">
          <div class="tile-icon">🏷️</div>
          <div class="tile-content">
            <h3>Discounts</h3>
            <p>Manage discount campaigns</p>
          </div>
        </button>

        <button class="dashboard-tile success">
          <div class="tile-icon">💸</div>
          <div class="tile-content">
            <h3>Special Pricing</h3>
            <p>Promotional pricing rules</p>
          </div>
        </button>

        <button class="dashboard-tile success">
          <div class="tile-icon">🎪</div>
          <div class="tile-content">
            <h3>Campaigns</h3>
            <p>Marketing campaigns</p>
          </div>
        </button>

        <button class="dashboard-tile success">
          <div class="tile-icon">🎁</div>
          <div class="tile-content">
            <h3>Loyalty Program</h3>
            <p>Customer loyalty rewards</p>
          </div>
        </button>
      </div>
    </div>

    <!-- System & Hardware -->
    <div class="tile-section">
      <h2 class="section-title">🔧 System & Hardware</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile info" onclick={() => showVirtualKeyboard = true}>
          <div class="tile-icon">⌨️</div>
          <div class="tile-content">
            <h3>Virtual Keyboard</h3>
            <p>On-screen keyboard for touch devices</p>
          </div>
        </button>

        <button class="dashboard-tile info" onclick={() => showDatabaseViewer = true}>
          <div class="tile-icon">🗄️</div>
          <div class="tile-content">
            <h3>Database Viewer</h3>
            <p>View database records & tables</p>
          </div>
        </button>

        <button class="dashboard-tile info">
          <div class="tile-icon">🖨️</div>
          <div class="tile-content">
            <h3>Receipt Printer</h3>
            <p>Printer settings & config</p>
          </div>
        </button>

        <button class="dashboard-tile info">
          <div class="tile-icon">📱</div>
          <div class="tile-content">
            <h3>Barcode Scanner</h3>
            <p>Scanner configuration</p>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Product Management Modal -->
{#if showProductModal}
  <div class="modal-overlay" onclick={() => showProductModal = false}>
    <div class="product-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>{editingProduct ? '✏️ Edit Product' : '➕ Add New Product'}</h2>
        <button class="btn-close" onclick={() => showProductModal = false}>✕</button>
      </div>

      <div class="modal-content">
        <div class="form-grid">
          <div class="form-group">
            <label for="product-name">Product Name *</label>
            <div class="input-with-keyboard">
              <input
                type="text"
                id="product-name"
                bind:value={newProduct.name}
                placeholder="Enter product name..."
                class="form-input"
                required
              />
              {#if !hasPhysicalKeyboard()}
                <button class="keyboard-btn" onclick={(e) => openVirtualKeyboard(e.target.previousElementSibling)}>⌨️</button>
              {/if}
            </div>
          </div>

          <div class="form-group">
            <label for="product-description">Description</label>
            <div class="input-with-keyboard">
              <textarea
                id="product-description"
                bind:value={newProduct.description}
                placeholder="Product description..."
                class="form-textarea"
                rows="3"
              ></textarea>
              {#if !hasPhysicalKeyboard()}
                <button class="keyboard-btn" onclick={(e) => openVirtualKeyboard(e.target.previousElementSibling)}>⌨️</button>
              {/if}
            </div>
          </div>

          <div class="form-group">
            <label for="product-price">Price (£) *</label>
            <input
              type="number"
              id="product-price"
              bind:value={newProduct.price}
              step="0.01"
              min="0"
              placeholder="0.00"
              class="form-input"
              required
            />
          </div>

          <div class="form-group">
            <label for="product-barcode">Barcode</label>
            <div class="input-with-keyboard">
              <input
                type="text"
                id="product-barcode"
                bind:value={newProduct.barcode}
                placeholder="Product barcode..."
                class="form-input"
              />
              {#if !hasPhysicalKeyboard()}
                <button class="keyboard-btn" onclick={(e) => openVirtualKeyboard(e.target.previousElementSibling)}>⌨️</button>
              {/if}
            </div>
          </div>

          <div class="form-group">
            <label for="product-category">Category</label>
            <select bind:value={newProduct.category_id} class="form-select">
              <option value={undefined}>No Category</option>
              {#each categories as category}
                <option value={category.id}>{category.icon} {category.name}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="product-vat">VAT Rate</label>
            <select bind:value={newProduct.vat_rate} class="form-select">
              <option value={0.2}>Standard Rate (20%)</option>
              <option value={0.05}>Reduced Rate (5%)</option>
              <option value={0}>Zero Rate (0%)</option>
            </select>
          </div>

          <div class="form-group">
            <label for="product-color">Product Color</label>
            <div class="color-picker">
              <input
                type="color"
                id="product-color"
                bind:value={newProduct.color}
                class="color-input"
              />
              <span class="color-preview" style="background-color: {newProduct.color}"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-actions">
        <button
          class="btn btn-primary"
          onclick={saveProduct}
          disabled={!newProduct.name || newProduct.price <= 0}
        >
          {editingProduct ? 'Update Product' : 'Create Product'}
        </button>
        <button class="btn btn-secondary" onclick={() => showProductModal = false}>
          Cancel
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- Category Management Modal -->
{#if showCategoryModal}
  <div class="modal-overlay" onclick={() => showCategoryModal = false}>
    <div class="category-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>{editingCategory ? '✏️ Edit Category' : '📁 Add New Category'}</h2>
        <button class="btn-close" onclick={() => showCategoryModal = false}>✕</button>
      </div>

      <div class="modal-content">
        <div class="form-grid">
          <div class="form-group">
            <label for="category-name">Category Name *</label>
            <div class="input-with-keyboard">
              <input
                type="text"
                id="category-name"
                bind:value={newCategory.name}
                placeholder="Enter category name..."
                class="form-input"
                required
              />
              {#if !hasPhysicalKeyboard()}
                <button class="keyboard-btn" onclick={(e) => openVirtualKeyboard(e.target.previousElementSibling)}>⌨️</button>
              {/if}
            </div>
          </div>

          <div class="form-group">
            <label for="category-description">Description</label>
            <div class="input-with-keyboard">
              <textarea
                id="category-description"
                bind:value={newCategory.description}
                placeholder="Category description..."
                class="form-textarea"
                rows="3"
              ></textarea>
              {#if !hasPhysicalKeyboard()}
                <button class="keyboard-btn" onclick={(e) => openVirtualKeyboard(e.target.previousElementSibling)}>⌨️</button>
              {/if}
            </div>
          </div>

          <div class="form-group">
            <label for="category-icon">Icon</label>
            <div class="icon-picker">
              <input
                type="text"
                id="category-icon"
                bind:value={newCategory.icon}
                placeholder="📦"
                class="form-input icon-input"
                maxlength="2"
              />
              <div class="icon-suggestions">
                {#each ['📦', '🍕', '📱', '👕', '🏠', '🚗', '💊', '📚', '🎮', '🎵'] as icon}
                  <button class="icon-btn" onclick={() => newCategory.icon = icon}>{icon}</button>
                {/each}
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="category-color">Category Color</label>
            <div class="color-picker">
              <input
                type="color"
                id="category-color"
                bind:value={newCategory.color}
                class="color-input"
              />
              <span class="color-preview" style="background-color: {newCategory.color}"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-actions">
        <button
          class="btn btn-primary"
          onclick={saveCategory}
          disabled={!newCategory.name}
        >
          {editingCategory ? 'Update Category' : 'Create Category'}
        </button>
        <button class="btn btn-secondary" onclick={() => showCategoryModal = false}>
          Cancel
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- Product List Modal -->
{#if showProductList}
  <div class="modal-overlay" onclick={() => showProductList = false}>
    <div class="list-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>🛍️ Manage Products</h2>
        <button class="btn-close" onclick={() => showProductList = false}>✕</button>
      </div>

      <div class="modal-content">
        <div class="list-controls">
          <button class="btn btn-primary" onclick={() => openProductModal()}>
            ➕ Add New Product
          </button>
          <div class="category-filter">
            <select bind:value={selectedCategory} onchange={filterProducts} class="form-select">
              <option value={null}>All Categories</option>
              {#each categories as category}
                <option value={category.id}>{category.icon} {category.name}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="products-list">
          {#each filteredProducts as product}
            <div class="product-item" style="--product-color: {product.color}">
              <div class="product-info">
                <h4>{product.name}</h4>
                <p>{product.description || 'No description'}</p>
                <div class="product-details">
                  <span class="price">{formatCurrency(product.price)}</span>
                  {#if product.barcode}
                    <span class="barcode">🏷️ {product.barcode}</span>
                  {/if}
                  <span class="vat">VAT: {(product.vat_rate * 100)}%</span>
                </div>
              </div>
              <div class="product-actions">
                <button class="btn btn-secondary" onclick={() => openProductModal(product)}>
                  ✏️ Edit
                </button>
                <button class="btn btn-danger" onclick={() => deleteProduct(product.id!)}>
                  🗑️ Delete
                </button>
              </div>
            </div>
          {/each}

          {#if filteredProducts.length === 0}
            <div class="empty-list">
              <div class="empty-icon">📦</div>
              <p>No products found</p>
              <button class="btn btn-primary" onclick={() => openProductModal()}>
                Add Your First Product
              </button>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Category List Modal -->
{#if showCategoryList}
  <div class="modal-overlay" onclick={() => showCategoryList = false}>
    <div class="list-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>📂 Manage Categories</h2>
        <button class="btn-close" onclick={() => showCategoryList = false}>✕</button>
      </div>

      <div class="modal-content">
        <div class="list-controls">
          <button class="btn btn-primary" onclick={() => openCategoryModal()}>
            ➕ Add New Category
          </button>
        </div>

        <div class="categories-list">
          {#each categories as category}
            <div class="category-item" style="--category-color: {category.color}">
              <div class="category-info">
                <div class="category-icon">{category.icon}</div>
                <div class="category-details">
                  <h4>{category.name}</h4>
                  <p>{category.description || 'No description'}</p>
                  <span class="product-count">
                    {products.filter(p => p.category_id === category.id).length} products
                  </span>
                </div>
              </div>
              <div class="category-actions">
                <button class="btn btn-secondary" onclick={() => openCategoryModal(category)}>
                  ✏️ Edit
                </button>
                <button class="btn btn-danger" onclick={() => deleteCategory(category.id!)}>
                  🗑️ Delete
                </button>
              </div>
            </div>
          {/each}

          {#if categories.length === 0}
            <div class="empty-list">
              <div class="empty-icon">📁</div>
              <p>No categories found</p>
              <button class="btn btn-primary" onclick={() => openCategoryModal()}>
                Add Your First Category
              </button>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Virtual Keyboard Modal -->
{#if showVirtualKeyboard}
  <div class="modal-overlay" onclick={() => showVirtualKeyboard = false}>
    <div class="keyboard-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>⌨️ Virtual Keyboard</h2>
        <button class="btn-close" onclick={() => showVirtualKeyboard = false}>✕</button>
      </div>

      <div class="keyboard-content">
        <div class="keyboard-display">
          <input
            type="text"
            bind:value={keyboardValue}
            placeholder="Type here..."
            class="keyboard-input"
            readonly
          />
        </div>

        <div class="keyboard-grid">
          <!-- Number row -->
          <div class="keyboard-row">
            {#each ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'] as key}
              <button class="key-btn" onclick={() => typeKey(key)}>{key}</button>
            {/each}
            <button class="key-btn backspace-btn" onclick={() => typeKey('Backspace')}>⌫</button>
          </div>

          <!-- Top row -->
          <div class="keyboard-row">
            {#each ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'] as key}
              <button class="key-btn" onclick={() => typeKey(key)}>{key}</button>
            {/each}
          </div>

          <!-- Middle row -->
          <div class="keyboard-row">
            {#each ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'] as key}
              <button class="key-btn" onclick={() => typeKey(key)}>{key}</button>
            {/each}
          </div>

          <!-- Bottom row -->
          <div class="keyboard-row">
            {#each ['Z', 'X', 'C', 'V', 'B', 'N', 'M'] as key}
              <button class="key-btn" onclick={() => typeKey(key)}>{key}</button>
            {/each}
          </div>

          <!-- Space and controls -->
          <div class="keyboard-row">
            <button class="key-btn space-btn" onclick={() => typeKey('Space')}>Space</button>
            <button class="key-btn" onclick={() => typeKey('.')}>.</button>
            <button class="key-btn" onclick={() => typeKey(',')}>,</button>
          </div>
        </div>

        <div class="keyboard-actions">
          <button class="btn btn-primary" onclick={applyKeyboardInput}>
            Apply
          </button>
          <button class="btn btn-secondary" onclick={() => showVirtualKeyboard = false}>
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Database Viewer Modal -->
{#if showDatabaseViewer}
  <div class="modal-overlay" onclick={() => showDatabaseViewer = false}>
    <div class="database-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>🗄️ Database Viewer</h2>
        <button class="btn-close" onclick={() => showDatabaseViewer = false}>✕</button>
      </div>

      <div class="database-content">
        <div class="database-controls">
          <label for="table-select">Select Table:</label>
          <select
            id="table-select"
            bind:value={selectedTable}
            onchange={() => loadTableData(selectedTable)}
            class="form-select"
          >
            <option value="">Choose a table...</option>
            {#each databaseTables as table}
              <option value={table}>{table}</option>
            {/each}
          </select>
        </div>

        {#if selectedTable && tableData.length > 0}
          <div class="table-viewer">
            <h3>Table: {selectedTable}</h3>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    {#each Object.keys(tableData[0]) as column}
                      <th>{column}</th>
                    {/each}
                  </tr>
                </thead>
                <tbody>
                  {#each tableData as row}
                    <tr>
                      {#each Object.values(row) as value}
                        <td>{value}</td>
                      {/each}
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
            <div class="table-info">
              <p>Total records: {tableData.length}</p>
            </div>
          </div>
        {:else if selectedTable}
          <div class="empty-table">
            <p>No data found in table: {selectedTable}</p>
          </div>
        {:else}
          <div class="no-table-selected">
            <p>Select a table to view its contents</p>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  /* Global Styles */
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  /* Dashboard Container */
  .dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  /* Dashboard Header */
  .dashboard-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .dashboard-title {
    margin: 0;
    font-size: 2.2rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .business-info {
    display: flex;
    gap: 1rem;
    font-size: 1rem;
    opacity: 0.9;
  }

  .business-name {
    font-weight: 600;
  }

  .business-location {
    opacity: 0.8;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .current-time {
    text-align: right;
  }

  .time {
    font-size: 2rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
  }

  .date {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .till-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: bold;
  }

  .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ef4444;
  }

  .status-indicator.open {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  }

  /* Stats Bar */
  .stats-bar {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 1.5rem 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 0.25rem;
  }

  .stat-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
  }

  /* Dashboard Grid */
  .dashboard-grid {
    flex: 1;
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    align-items: start;
  }

  .tile-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .section-title {
    margin: 0 0 1.5rem 0;
    font-size: 1.4rem;
    font-weight: bold;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .tiles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  /* Dashboard Tiles */
  .dashboard-tile {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
  }

  .dashboard-tile:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }

  .dashboard-tile.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  }

  .dashboard-tile.primary:hover {
    box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
  }

  .dashboard-tile.secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    border-color: #6b7280;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  }

  .dashboard-tile.secondary:hover {
    box-shadow: 0 12px 30px rgba(107, 114, 128, 0.4);
  }

  .dashboard-tile.accent {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border-color: #8b5cf6;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  }

  .dashboard-tile.accent:hover {
    box-shadow: 0 12px 30px rgba(139, 92, 246, 0.4);
  }

  .dashboard-tile.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border-color: #f59e0b;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
  }

  .dashboard-tile.warning:hover {
    box-shadow: 0 12px 30px rgba(245, 158, 11, 0.4);
  }

  .dashboard-tile.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-color: #10b981;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }

  .dashboard-tile.success:hover {
    box-shadow: 0 12px 30px rgba(16, 185, 129, 0.4);
  }

  .dashboard-tile.info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    border-color: #06b6d4;
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
  }

  .dashboard-tile.info:hover {
    box-shadow: 0 12px 30px rgba(6, 182, 212, 0.4);
  }

  .tile-icon {
    font-size: 2.5rem;
    opacity: 0.9;
  }

  .tile-content {
    flex: 1;
  }

  .tile-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .tile-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.3;
  }

  .tile-arrow {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .dashboard-tile:hover .tile-arrow {
    opacity: 1;
    transform: translateX(4px);
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .dashboard-grid {
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }

    .tiles-grid {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .dashboard-header {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .dashboard-title {
      font-size: 1.8rem;
    }

    .header-right {
      flex-direction: column;
      gap: 1rem;
    }

    .stats-bar {
      grid-template-columns: 1fr;
      padding: 1rem;
    }

    .dashboard-grid {
      grid-template-columns: 1fr;
      padding: 1rem;
      gap: 1rem;
    }

    .tile-section {
      padding: 1.5rem;
    }

    .tiles-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .dashboard-tile {
      padding: 1rem;
    }

    .tile-icon {
      font-size: 2rem;
    }

    .tile-content h3 {
      font-size: 1rem;
    }

    .tile-content p {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .dashboard-title {
      font-size: 1.5rem;
    }

    .business-info {
      flex-direction: column;
      gap: 0.5rem;
    }

    .time {
      font-size: 1.5rem;
    }

    .tiles-grid {
      grid-template-columns: 1fr;
    }

    .dashboard-tile {
      padding: 1.5rem 1rem;
    }
  }

  /* Cart Panel */
  .cart-panel {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 8rem);
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
  }

  .cart-count {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
  }

  .cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1rem;
  }

  .empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-subtitle {
    font-size: 0.9rem;
    opacity: 0.7;
  }

  .cart-item {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid var(--item-color, #3b82f6);
    transition: all 0.3s ease;
  }

  .cart-item:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .item-info {
    flex: 1;
  }

  .item-name {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: bold;
    color: #1e293b;
  }

  .item-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #64748b;
  }

  .item-price {
    font-weight: 600;
  }

  .item-quantity {
    color: #059669;
    font-weight: bold;
  }

  .item-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .item-total {
    font-size: 1.1rem;
    font-weight: bold;
    color: #059669;
  }

  .btn-remove {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    border-radius: 8px;
    padding: 0.25rem 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
  }

  .btn-remove:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
  }

  /* Cart Summary */
  .cart-summary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e2e8f0;
  }

  .summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
  }

  .total-line {
    font-size: 1.2rem;
    font-weight: bold;
    color: #059669;
    border-top: 2px solid #d1d5db;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
  }

  .payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .payment-btn {
    padding: 1rem;
    font-size: 1rem;
    font-weight: bold;
  }

  /* Buttons */
  .btn {
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
  }

  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .receipt-modal {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .receipt-header {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .receipt-header h2 {
    margin: 0;
    font-size: 1.5rem;
  }

  .btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
  }

  .btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .receipt-content {
    padding: 2rem;
  }

  .receipt-business {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px dashed #d1d5db;
  }

  .receipt-business h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #1e293b;
  }

  .receipt-business p {
    margin: 0.25rem 0;
    color: #64748b;
  }

  .receipt-transaction {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .receipt-transaction p {
    margin: 0.5rem 0;
    color: #374151;
  }

  .receipt-items {
    margin-bottom: 1.5rem;
  }

  .receipt-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .receipt-item-name {
    font-weight: 600;
    color: #1e293b;
  }

  .receipt-item-qty {
    color: #059669;
    font-weight: bold;
  }

  .receipt-item-price {
    font-weight: bold;
    color: #1e293b;
  }

  .receipt-totals {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .receipt-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .receipt-total {
    font-size: 1.2rem;
    border-top: 2px solid #d1d5db;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }

  .receipt-footer {
    text-align: center;
    color: #64748b;
    font-style: italic;
  }

  .receipt-actions {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
  }

  .receipt-actions .btn {
    flex: 1;
  }

  /* Product Management Modal Styles */
  .product-modal,
  .category-modal,
  .list-modal,
  .keyboard-modal,
  .database-modal {
    background: white;
    border-radius: 16px;
    max-width: 800px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: bold;
    color: #374151;
    font-size: 0.9rem;
  }

  .input-with-keyboard {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .form-input,
  .form-textarea,
  .form-select {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
  }

  .form-input:focus,
  .form-textarea:focus,
  .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-textarea {
    resize: vertical;
    min-height: 80px;
  }

  .keyboard-btn {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    border: none;
    border-radius: 6px;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-width: 2.5rem;
  }

  .keyboard-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
  }

  .color-picker {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .color-input {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
  }

  .color-preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
  }

  .icon-picker {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .icon-input {
    text-align: center;
    font-size: 1.5rem;
  }

  .icon-suggestions {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
  }

  .icon-btn {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border: 2px solid #d1d5db;
    border-radius: 8px;
    padding: 0.75rem;
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.3s ease;
  }

  .icon-btn:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    transform: scale(1.1);
  }

  /* List Modal Styles */
  .list-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
  }

  .category-filter {
    min-width: 200px;
  }

  .products-list,
  .categories-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 500px;
    overflow-y: auto;
  }

  .product-item,
  .category-item {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid var(--product-color, var(--category-color, #3b82f6));
    transition: all 0.3s ease;
  }

  .product-item:hover,
  .category-item:hover {
    transform: translateX(4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .product-info,
  .category-info {
    flex: 1;
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .category-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--category-color, #3b82f6);
    border-radius: 12px;
    color: white;
  }

  .category-details,
  .product-info {
    flex: 1;
  }

  .product-info h4,
  .category-details h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #1e293b;
  }

  .product-info p,
  .category-details p {
    margin: 0 0 0.5rem 0;
    color: #64748b;
    font-size: 0.9rem;
  }

  .product-details {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    font-size: 0.85rem;
  }

  .price {
    color: #059669;
    font-weight: bold;
  }

  .barcode {
    color: #6b7280;
  }

  .vat {
    color: #8b5cf6;
  }

  .product-count {
    color: #3b82f6;
    font-weight: bold;
    font-size: 0.85rem;
  }

  .product-actions,
  .category-actions {
    display: flex;
    gap: 0.75rem;
  }

  .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
  }

  .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
  }

  .empty-list {
    text-align: center;
    padding: 3rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  /* Virtual Keyboard Styles */
  .keyboard-content {
    padding: 2rem;
  }

  .keyboard-display {
    margin-bottom: 2rem;
  }

  .keyboard-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1.2rem;
    text-align: center;
    background: #f9fafb;
  }

  .keyboard-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
  }

  .keyboard-row {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
  }

  .key-btn {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border: 2px solid #d1d5db;
    border-radius: 8px;
    padding: 0.75rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .key-btn:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  }

  .key-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
  }

  .space-btn {
    min-width: 12rem;
  }

  .backspace-btn {
    min-width: 4rem;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
  }

  .backspace-btn:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
  }

  .keyboard-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  /* Database Viewer Styles */
  .database-content {
    padding: 2rem;
  }

  .database-controls {
    margin-bottom: 2rem;
  }

  .database-controls label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #374151;
  }

  .table-viewer {
    margin-top: 2rem;
  }

  .table-viewer h3 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 1.2rem;
  }

  .table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
  }

  .data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
  }

  .data-table th {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    padding: 0.75rem;
    text-align: left;
    font-weight: bold;
    color: #374151;
    border-bottom: 2px solid #d1d5db;
  }

  .data-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
    color: #1e293b;
  }

  .data-table tr:hover {
    background: #f9fafb;
  }

  .table-info {
    text-align: center;
    color: #64748b;
    font-size: 0.9rem;
  }

  .empty-table,
  .no-table-selected {
    text-align: center;
    padding: 3rem;
    color: #64748b;
  }

  /* Responsive Design for Modals */
  @media (max-width: 768px) {
    .product-modal,
    .category-modal,
    .list-modal,
    .keyboard-modal,
    .database-modal {
      width: 98%;
      max-height: 95vh;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .list-controls {
      flex-direction: column;
      gap: 1rem;
    }

    .category-filter {
      min-width: auto;
      width: 100%;
    }

    .product-item,
    .category-item {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .product-actions,
    .category-actions {
      justify-content: center;
    }

    .keyboard-row {
      flex-wrap: wrap;
    }

    .key-btn {
      min-width: 2.5rem;
      height: 2.5rem;
      font-size: 0.9rem;
    }

    .space-btn {
      min-width: 8rem;
    }
  }
</style>
