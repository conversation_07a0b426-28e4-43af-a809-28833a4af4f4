<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { onMount } from "svelte";

  // Types
  interface Product {
    id: number;
    name: string;
    description?: string;
    price: number;
    category_id?: number;
    vat_rate_id: number;
    barcode?: string;
    color: string;
    is_active: boolean;
    image?: string;
  }

  interface ProductTile {
    id: number;
    name: string;
    color: string;
    image?: string;
    products: Product[];
  }

  interface CartItem {
    product_id: number;
    name: string;
    price: number;
    quantity: number;
    vat_rate: number;
    color: string;
    barcode?: string;
  }

  interface Transaction {
    id?: number;
    transaction_number: string;
    subtotal: number;
    vat_amount: number;
    total: number;
    items: CartItem[];
    payment_method: string;
  }

  // State
  let products = $state<Product[]>([]);
  let productTiles = $state<ProductTile[]>([]);
  let cart = $state<CartItem[]>([]);
  let showPayment = $state(false);
  let showFullScreenPayment = $state(false);
  let lastTransaction = $state<Transaction | null>(null);
  let showReceipt = $state(false);
  let showNewProductModal = $state(false);
  let showTileProductsModal = $state(false);
  let showGoodsModal = $state(false);
  let selectedTile = $state<ProductTile | null>(null);
  let selectedCartItemId = $state<number | null>(null);
  let barcodeInput = $state("");
  let newProduct = $state({ name: "", price: 0, barcode: "" });
  let manualProduct = $state({ name: "", price: 0, quantity: 1 });
  let paymentAmount = $state(0);
  let selectedPaymentMethod = $state<string>("");
  let cartContainer: HTMLElement;

  // Computed values
  let cartTotal = $derived(
    cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  );

  let cartVAT = $derived(
    cart.reduce((vat, item) => {
      const lineTotal = item.price * item.quantity;
      return vat + (lineTotal * item.vat_rate / (1 + item.vat_rate));
    }, 0)
  );

  let cartSubtotal = $derived(cartTotal - cartVAT);
  let changeAmount = $derived(paymentAmount - cartTotal);

  // Sample data for when Tauri API is not available
  const sampleProducts: Product[] = [
    {
      id: 1,
      name: "Coffee",
      description: "Premium coffee blend",
      price: 2.50,
      category_id: 1,
      vat_rate_id: 1,
      barcode: "123456789",
      color: "#8B4513",
      is_active: true,
    },
    {
      id: 2,
      name: "Tea",
      description: "English breakfast tea",
      price: 2.00,
      category_id: 1,
      vat_rate_id: 1,
      barcode: "987654321",
      color: "#228B22",
      is_active: true,
    },
    {
      id: 3,
      name: "Sandwich",
      description: "Fresh sandwich",
      price: 4.50,
      category_id: 2,
      vat_rate_id: 1,
      barcode: "456789123",
      color: "#DAA520",
      is_active: true,
    },
    {
      id: 4,
      name: "Pastry",
      description: "Delicious pastry",
      price: 3.00,
      category_id: 2,
      vat_rate_id: 1,
      barcode: "789123456",
      color: "#DEB887",
      is_active: true,
    },
    {
      id: 5,
      name: "Juice",
      description: "Fresh orange juice",
      price: 3.50,
      category_id: 1,
      vat_rate_id: 1,
      barcode: "321654987",
      color: "#FFA500",
      is_active: true,
    },
    {
      id: 6,
      name: "Chips",
      description: "Crispy potato chips",
      price: 1.50,
      category_id: 3,
      vat_rate_id: 1,
      barcode: "654987321",
      color: "#FFD700",
      is_active: true,
    },
  ];

  const sampleProductTiles: ProductTile[] = [
    {
      id: 1,
      name: "Hot Drinks",
      color: "#8B4513",
      image: "☕",
      products: [
        { id: 1, name: "Coffee", price: 2.50, barcode: "123456789", color: "#8B4513", vat_rate_id: 1, is_active: true },
        { id: 2, name: "Tea", price: 2.00, barcode: "987654321", color: "#228B22", vat_rate_id: 1, is_active: true },
        { id: 7, name: "Hot Chocolate", price: 3.00, barcode: "111222333", color: "#D2691E", vat_rate_id: 1, is_active: true },
      ]
    },
    {
      id: 2,
      name: "Cold Drinks",
      color: "#3B82F6",
      image: "🥤",
      products: [
        { id: 5, name: "Juice", price: 3.50, barcode: "321654987", color: "#FFA500", vat_rate_id: 1, is_active: true },
        { id: 8, name: "Soda", price: 2.00, barcode: "444555666", color: "#FF6347", vat_rate_id: 1, is_active: true },
        { id: 9, name: "Water", price: 1.50, barcode: "777888999", color: "#87CEEB", vat_rate_id: 1, is_active: true },
      ]
    },
    {
      id: 3,
      name: "Food",
      color: "#10B981",
      image: "🍽️",
      products: [
        { id: 3, name: "Sandwich", price: 4.50, barcode: "456789123", color: "#DAA520", vat_rate_id: 1, is_active: true },
        { id: 4, name: "Pastry", price: 3.00, barcode: "789123456", color: "#DEB887", vat_rate_id: 1, is_active: true },
      ]
    },
    {
      id: 4,
      name: "Snacks",
      color: "#F59E0B",
      image: "🍿",
      products: [
        { id: 6, name: "Chips", price: 1.50, barcode: "654987321", color: "#FFD700", vat_rate_id: 1, is_active: true },
      ]
    },
  ];

  // Initialize with sample data
  productTiles = sampleProductTiles;

  // Load data on mount
  onMount(async () => {
    await loadProducts();
    await loadCart();
  });

  async function loadProducts() {
    try {
      products = await invoke("get_products");
    } catch (error) {
      console.error("Failed to load products:", error);
      // Fallback to sample data
      products = sampleProducts;
    }
  }

  async function loadCart() {
    try {
      cart = await invoke("get_cart");
    } catch (error) {
      console.error("Failed to load cart:", error);
      // Fallback to empty cart
      cart = [];
    }
  }

  // Audio feedback function
  function playClickSound() {
    // Create a simple click sound using Web Audio API
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      console.log("Audio not supported");
    }
  }

  // Auto-scroll cart to bottom
  function scrollCartToBottom() {
    if (cartContainer) {
      cartContainer.scrollTop = cartContainer.scrollHeight;
    }
  }

  // Enhanced barcode search with automatic product creation
  function searchByBarcode() {
    playClickSound();
    if (!barcodeInput.trim()) return;

    // First, search in all products from all tiles
    let foundProduct = null;
    for (const tile of productTiles) {
      foundProduct = tile.products.find(p => p.barcode === barcodeInput.trim());
      if (foundProduct) break;
    }

    if (foundProduct) {
      addToCart(foundProduct);
      barcodeInput = "";
    } else {
      // Show new product modal for unknown barcode
      newProduct.barcode = barcodeInput.trim();
      showNewProductModal = true;
    }
  }

  // Add product to cart with audio feedback and auto-scroll
  function addToCart(product: Product) {
    playClickSound();

    const existingItem = cart.find(item => item.product_id === product.id);
    if (existingItem) {
      existingItem.quantity += 1;
      cart = [...cart]; // Trigger reactivity
    } else {
      cart = [...cart, {
        product_id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        vat_rate: 0, // VAT-free as requested
        color: product.color,
        barcode: product.barcode,
      }];
    }

    // Auto-scroll cart to show new item
    setTimeout(scrollCartToBottom, 100);
  }

  // Create new product from barcode scan
  async function createNewProduct() {
    if (!newProduct.name || newProduct.price <= 0) return;

    const product: Product = {
      id: Date.now(),
      name: newProduct.name,
      price: newProduct.price,
      barcode: newProduct.barcode,
      color: "#6B7280",
      vat_rate_id: 0, // VAT-free
      is_active: true,
    };

    try {
      // Try to save to database
      await invoke("create_product", product);
    } catch (error) {
      console.error("Failed to save product:", error);
    }

    // Add to first available tile or create new one
    if (productTiles.length > 0) {
      productTiles[0].products.push(product);
      productTiles = [...productTiles]; // Trigger reactivity
    }

    // Add to cart immediately
    addToCart(product);

    // Reset form and close modal
    newProduct = { name: "", price: 0, barcode: "" };
    showNewProductModal = false;
    barcodeInput = "";
  }

  // Open tile products modal
  function openTileProducts(tile: ProductTile) {
    playClickSound();
    selectedTile = tile;
    showTileProductsModal = true;
  }

  // Select cart item for centralized controls
  function selectCartItem(productId: number) {
    playClickSound();
    selectedCartItemId = productId;
  }

  // Remove item from cart (centralized)
  function removeSelectedItem() {
    playClickSound();
    if (selectedCartItemId) {
      cart = cart.filter(item => item.product_id !== selectedCartItemId);
      selectedCartItemId = null;
    }
  }

  // Update selected item quantity (centralized)
  function updateSelectedQuantity(change: number) {
    playClickSound();
    if (selectedCartItemId) {
      const item = cart.find(item => item.product_id === selectedCartItemId);
      if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
          removeSelectedItem();
        } else {
          cart = [...cart]; // Trigger reactivity
        }
      }
    }
  }

  // Set selected item quantity directly
  function setSelectedQuantity(quantity: number) {
    playClickSound();
    if (selectedCartItemId && quantity > 0) {
      const item = cart.find(item => item.product_id === selectedCartItemId);
      if (item) {
        item.quantity = quantity;
        cart = [...cart]; // Trigger reactivity
      }
    }
  }

  // Open goods modal for manual entry
  function openGoodsModal() {
    playClickSound();
    showGoodsModal = true;
    manualProduct = { name: "", price: 0, quantity: 1 };
  }

  // Add manual product to cart
  function addManualProduct() {
    if (!manualProduct.name || manualProduct.price <= 0 || manualProduct.quantity <= 0) return;

    // Create a temporary product object
    const tempProduct: Product = {
      id: Date.now(), // Use timestamp as temporary ID
      name: manualProduct.name,
      price: manualProduct.price,
      vat_rate_id: 1,
      color: "#6b7280", // Default gray color
      is_active: true,
    };

    // Add to cart with specified quantity
    for (let i = 0; i < manualProduct.quantity; i++) {
      addToCart(tempProduct);
    }

    // Reset form and close modal
    manualProduct = { name: "", price: 0, quantity: 1 };
    showGoodsModal = false;
  }

  // Clear entire cart
  function clearCart() {
    playClickSound();
    cart = [];
  }

  // Open full-screen payment interface
  function openPayment() {
    playClickSound();
    if (cart.length === 0) return;
    showFullScreenPayment = true;
    paymentAmount = 0;
    selectedPaymentMethod = "";
  }

  // Process payment
  async function processPayment(paymentMethod: string) {
    if (paymentMethod === 'cash' && paymentAmount < cartTotal) {
      alert("Insufficient cash received");
      return;
    }

    try {
      lastTransaction = await invoke("process_transaction", { paymentMethod });
    } catch (error) {
      console.error("Failed to process payment:", error);
      // Fallback: process payment manually
      lastTransaction = {
        transaction_number: `TXN${Date.now()}`,
        subtotal: cartSubtotal,
        vat_amount: cartVAT,
        total: cartTotal,
        items: [...cart],
        payment_method: paymentMethod,
      };
    }

    cart = [];
    showFullScreenPayment = false;
    showReceipt = true;
    paymentAmount = 0;
  }

  // Print last receipt
  function printLastReceipt() {
    playClickSound();
    if (lastTransaction) {
      showReceipt = true;
    }
  }

  // Open till
  function openTill() {
    playClickSound();
    alert("Till opened successfully!");
  }

  function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  }
</script>

<!-- Streamlined POS Interface -->
<div class="pos-container">
  <!-- Header with Navigation -->
  <header class="pos-header">
    <div class="header-left">
      <a href="/" class="back-btn">🏠 Dashboard</a>
      <h1 class="pos-title">🏪 Point of Sale</h1>
    </div>
    <div class="header-actions">
      <button class="btn btn-secondary" onclick={clearCart}>
        🗑️ Clear Cart
      </button>
    </div>
  </header>

  <!-- Main Content -->
  <div class="pos-main">
    <!-- Left Panel - Product Tiles (4x4 Grid) -->
    <div class="products-panel">
      <div class="product-tiles-grid">
        {#each productTiles as tile}
          <button
            class="product-tile"
            style="--tile-color: {tile.color}"
            onclick={() => openTileProducts(tile)}
          >
            <div class="tile-image">{tile.image || '📦'}</div>
            <div class="tile-name">{tile.name}</div>
          </button>
        {/each}

        <!-- Fill remaining slots with empty tiles -->
        {#each Array(16 - productTiles.length) as _, i}
          <button class="product-tile empty-tile">
            <div class="tile-image">➕</div>
            <div class="tile-name">Add Category</div>
          </button>
        {/each}
      </div>
    </div>

    <!-- Right Panel - Enhanced Cart with Barcode Scanner -->
    <div class="cart-panel">
      <!-- Barcode Scanner Section (moved above cart) -->
      <div class="barcode-section">
        <div class="barcode-input-group">
          <input
            type="text"
            bind:value={barcodeInput}
            placeholder="Scan or enter barcode..."
            class="barcode-input"
            onkeydown={(e) => e.key === 'Enter' && searchByBarcode()}
          />
          <button class="btn btn-primary" onclick={searchByBarcode}>
            🔍 Search
          </button>
          <button class="btn btn-accent" onclick={openGoodsModal}>
            📦 Goods
          </button>
        </div>
      </div>

      <div class="cart-header">
        <h2 class="section-title">🛒 Shopping Cart</h2>
        <div class="cart-count">{cart.length} items</div>
      </div>

      <div class="cart-items" bind:this={cartContainer}>
        {#if cart.length === 0}
          <div class="empty-cart">
            <div class="empty-icon">🛒</div>
            <p>Your cart is empty</p>
            <p class="empty-subtitle">Scan items or select from categories!</p>
          </div>
        {:else}
          {#each cart as item}
            <div
              class="cart-item"
              class:selected={selectedCartItemId === item.product_id}
              style="--item-color: {item.color}"
              onclick={() => selectCartItem(item.product_id)}
            >
              <div class="item-info">
                <div class="item-name">{item.name}</div>
                <div class="item-details">
                  <span class="item-price">{formatCurrency(item.price)}</span>
                  <span class="item-quantity">Qty: {item.quantity}</span>
                </div>
              </div>
              <div class="item-total">{formatCurrency(item.price * item.quantity)}</div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Centralized Quantity Controls -->
      {#if cart.length > 0}
        <div class="centralized-controls">
          {#if selectedCartItemId}
            {@const selectedItem = cart.find(item => item.product_id === selectedCartItemId)}
            {#if selectedItem}
              <div class="selected-item-info">
                <span class="selected-name">{selectedItem.name}</span>
                <span class="selected-price">{formatCurrency(selectedItem.price)}</span>
              </div>
              <div class="quantity-controls">
                <button class="qty-btn" onclick={() => updateSelectedQuantity(-1)}>➖</button>
                <input
                  type="number"
                  class="qty-input"
                  value={selectedItem.quantity}
                  min="1"
                  onchange={(e) => setSelectedQuantity(parseInt(e.target.value) || 1)}
                />
                <button class="qty-btn" onclick={() => updateSelectedQuantity(1)}>➕</button>
                <button class="btn-remove-selected" onclick={removeSelectedItem}>🗑️</button>
              </div>
            {/if}
          {:else}
            <div class="no-selection">
              <span>Click an item to modify quantity or remove</span>
            </div>
          {/if}
        </div>
      {/if}

      <!-- Streamlined Cart Summary -->
      {#if cart.length > 0}
        <div class="cart-summary">
          <div class="summary-line">
            <span>Subtotal:</span>
            <span>{formatCurrency(cartSubtotal)}</span>
          </div>
          <div class="summary-line">
            <span>VAT:</span>
            <span>{formatCurrency(cartVAT)}</span>
          </div>
          <div class="summary-line total-line">
            <span>Total:</span>
            <span>{formatCurrency(cartTotal)}</span>
          </div>

          <div class="payment-actions">
            <button
              class="btn btn-primary pay-btn"
              onclick={openPayment}
            >
              💳 Pay
            </button>
            <button
              class="btn btn-secondary receipt-btn"
              onclick={printLastReceipt}
              disabled={!lastTransaction}
            >
              🧾 Print Last Receipt
            </button>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Open Till Button -->
  <div class="till-controls">
    <button class="btn btn-accent till-btn" onclick={openTill}>
      💷 Open Till
    </button>
  </div>
</div>

<!-- Goods Modal (for manual product entry) -->
{#if showGoodsModal}
  <div class="modal-overlay" onclick={() => showGoodsModal = false}>
    <div class="new-product-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>📦 Manual Product Entry</h2>
        <button class="btn-close" onclick={() => showGoodsModal = false}>✕</button>
      </div>

      <div class="modal-content">
        <p class="info-text">Enter product details to add directly to cart:</p>

        <div class="form-group">
          <label for="manual-product-name">Product Name:</label>
          <input
            type="text"
            id="manual-product-name"
            bind:value={manualProduct.name}
            placeholder="Enter product name..."
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label for="manual-product-price">Price (£):</label>
          <input
            type="number"
            id="manual-product-price"
            bind:value={manualProduct.price}
            step="0.01"
            min="0"
            placeholder="0.00"
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label for="manual-product-quantity">Quantity:</label>
          <input
            type="number"
            id="manual-product-quantity"
            bind:value={manualProduct.quantity}
            min="1"
            placeholder="1"
            class="form-input"
          />
        </div>

        <p class="vat-info">✓ This product will be VAT-free (0% VAT)</p>
      </div>

      <div class="modal-actions">
        <button
          class="btn btn-primary"
          onclick={addManualProduct}
          disabled={!manualProduct.name || manualProduct.price <= 0 || manualProduct.quantity <= 0}
        >
          Add to Cart
        </button>
        <button class="btn btn-secondary" onclick={() => showGoodsModal = false}>
          Cancel
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- New Product Modal (for unknown barcodes) -->
{#if showNewProductModal}
  <div class="modal-overlay" onclick={() => showNewProductModal = false}>
    <div class="new-product-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>📦 New Product</h2>
        <button class="btn-close" onclick={() => showNewProductModal = false}>✕</button>
      </div>

      <div class="modal-content">
        <p class="barcode-info">Barcode: <strong>{newProduct.barcode}</strong></p>
        <p class="info-text">This barcode was not found. Please enter product details:</p>

        <div class="form-group">
          <label for="new-product-name">Product Name:</label>
          <input
            type="text"
            id="new-product-name"
            bind:value={newProduct.name}
            placeholder="Enter product name..."
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label for="new-product-price">Price (£):</label>
          <input
            type="number"
            id="new-product-price"
            bind:value={newProduct.price}
            step="0.01"
            min="0"
            placeholder="0.00"
            class="form-input"
          />
        </div>

        <p class="vat-info">✓ This product will be VAT-free (0% VAT)</p>
      </div>

      <div class="modal-actions">
        <button
          class="btn btn-primary"
          onclick={createNewProduct}
          disabled={!newProduct.name || newProduct.price <= 0}
        >
          Add Product & Continue
        </button>
        <button class="btn btn-secondary" onclick={() => showNewProductModal = false}>
          Cancel
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- Tile Products Modal (3x3 grid) -->
{#if showTileProductsModal && selectedTile}
  <div class="modal-overlay" onclick={() => showTileProductsModal = false}>
    <div class="tile-products-modal" onclick={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h2>{selectedTile.image} {selectedTile.name}</h2>
        <button class="btn-close" onclick={() => showTileProductsModal = false}>✕</button>
      </div>

      <div class="tile-products-grid">
        {#each selectedTile.products as product}
          <button
            class="tile-product-btn"
            style="--product-color: {product.color}"
            onclick={() => { addToCart(product); showTileProductsModal = false; }}
          >
            <div class="product-name">{product.name}</div>
            <div class="product-price">{formatCurrency(product.price)}</div>
            {#if product.barcode}
              <div class="product-barcode">🏷️ {product.barcode}</div>
            {/if}
          </button>
        {/each}

        <!-- Fill remaining slots with add buttons -->
        {#each Array(9 - selectedTile.products.length) as _, i}
          <button class="tile-product-btn add-product-btn">
            <div class="add-icon">➕</div>
            <div class="add-text">Add Product</div>
          </button>
        {/each}
      </div>
    </div>
  </div>
{/if}

<!-- Full-Screen Payment Interface -->
{#if showFullScreenPayment}
  <div class="fullscreen-payment">
    <div class="payment-header">
      <h1>💳 Payment</h1>
      <button class="btn-close-payment" onclick={() => showFullScreenPayment = false}>✕</button>
    </div>

    <div class="payment-main">
      <!-- Left Panel - Payment Methods -->
      <div class="payment-methods-panel">
        <h2>Select Payment Method</h2>

        <div class="payment-method-tiles">
          <button
            class="payment-method-tile {selectedPaymentMethod === 'cash' ? 'active' : ''}"
            onclick={() => selectedPaymentMethod = 'cash'}
          >
            <div class="payment-icon">💷</div>
            <div class="payment-name">Cash</div>
          </button>

          <button
            class="payment-method-tile {selectedPaymentMethod === 'card' ? 'active' : ''}"
            onclick={() => selectedPaymentMethod = 'card'}
          >
            <div class="payment-icon">💳</div>
            <div class="payment-name">Card</div>
          </button>
        </div>

        {#if selectedPaymentMethod === 'cash'}
          <div class="cash-payment-section">
            <div class="amount-input">
              <label>Amount Received:</label>
              <div class="amount-display">
                <span class="currency">£</span>
                <input
                  type="number"
                  bind:value={paymentAmount}
                  step="0.01"
                  min="0"
                  class="amount-input-field"
                />
              </div>
            </div>

            <div class="number-pad">
              <div class="number-grid">
                {#each [7, 8, 9, 4, 5, 6, 1, 2, 3, 0] as num}
                  <button class="number-btn" onclick={() => paymentAmount = parseFloat((paymentAmount.toString() + num).replace(/^0+/, '')) || 0}>
                    {num}
                  </button>
                {/each}
                <button class="number-btn" onclick={() => paymentAmount = parseFloat((paymentAmount.toString() + '.').replace(/^0+/, '')) || 0}>.</button>
                <button class="number-btn clear-btn" onclick={() => paymentAmount = 0}>Clear</button>
              </div>
            </div>

            {#if paymentAmount >= cartTotal}
              <div class="change-info">
                <span>Change: {formatCurrency(paymentAmount - cartTotal)}</span>
              </div>
            {/if}
          </div>
        {/if}

        {#if selectedPaymentMethod}
          <button
            class="btn btn-primary complete-payment-btn"
            onclick={() => processPayment(selectedPaymentMethod)}
            disabled={selectedPaymentMethod === 'cash' && paymentAmount < cartTotal}
          >
            Complete Payment
          </button>
        {/if}
      </div>

      <!-- Right Panel - Cart Summary -->
      <div class="payment-cart-panel">
        <h2>Order Summary</h2>

        <div class="payment-cart-items">
          {#each cart as item}
            <div class="payment-cart-item">
              <span class="item-name">{item.name}</span>
              <span class="item-qty">× {item.quantity}</span>
              <span class="item-total">{formatCurrency(item.price * item.quantity)}</span>
            </div>
          {/each}
        </div>

        <div class="payment-totals">
          <div class="total-line">
            <span>Subtotal:</span>
            <span>{formatCurrency(cartSubtotal)}</span>
          </div>
          <div class="total-line">
            <span>VAT:</span>
            <span>{formatCurrency(cartVAT)}</span>
          </div>
          <div class="total-line grand-total">
            <span>Total:</span>
            <span>{formatCurrency(cartTotal)}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Receipt Modal -->
{#if showReceipt && lastTransaction}
  <div class="modal-overlay" onclick={() => showReceipt = false}>
    <div class="receipt-modal" onclick={(e) => e.stopPropagation()}>
      <div class="receipt-header">
        <h2>🧾 Receipt</h2>
        <button class="btn-close" onclick={() => showReceipt = false}>✕</button>
      </div>

      <div class="receipt-content">
        <div class="receipt-business">
          <h3>Your Business Name</h3>
          <p>123 High Street, London, UK</p>
          <p>VAT: GB123456789</p>
        </div>

        <div class="receipt-transaction">
          <p><strong>Transaction:</strong> {lastTransaction.transaction_number}</p>
          <p><strong>Date:</strong> {new Date().toLocaleString('en-GB')}</p>
          <p><strong>Payment:</strong> {lastTransaction.payment_method}</p>
        </div>

        <div class="receipt-items">
          {#each lastTransaction.items as item}
            <div class="receipt-item">
              <span class="receipt-item-name">{item.name}</span>
              <span class="receipt-item-qty">× {item.quantity}</span>
              <span class="receipt-item-price">{formatCurrency(item.price * item.quantity)}</span>
            </div>
          {/each}
        </div>

        <div class="receipt-totals">
          <div class="receipt-line">
            <span>Subtotal:</span>
            <span>{formatCurrency(lastTransaction.subtotal)}</span>
          </div>
          <div class="receipt-line">
            <span>VAT:</span>
            <span>{formatCurrency(lastTransaction.vat_amount)}</span>
          </div>
          <div class="receipt-line receipt-total">
            <span><strong>Total:</strong></span>
            <span><strong>{formatCurrency(lastTransaction.total)}</strong></span>
          </div>
        </div>

        <div class="receipt-footer">
          <p>Thank you for your business!</p>
        </div>
      </div>

      <div class="receipt-actions">
        <button class="btn btn-primary" onclick={() => window.print()}>
          🖨️ Print Receipt
        </button>
        <button class="btn btn-secondary" onclick={() => showReceipt = false}>
          Close
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Global Styles */
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  /* POS Container */
  .pos-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  /* Enhanced Header */
  .pos-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: bold;
  }

  .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .pos-title {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  /* Barcode Scanner Section */
  .barcode-section {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .barcode-input-group {
    display: flex;
    gap: 1rem;
    flex: 1;
    max-width: 500px;
  }

  .barcode-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .quick-actions {
    display: flex;
    gap: 1rem;
  }

  /* Main Content */
  .pos-main {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    padding: 1rem;
    min-height: 0;
  }

  /* Products Panel - 4x4 Tile Grid */
  .products-panel {
    background: white;
    border-radius: 16px;
    padding: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .product-tiles-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 0.75rem;
    height: 100%;
    min-height: 500px;
  }

  .product-tile {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    border: none;
    border-radius: 12px;
    padding: 1rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(162, 155, 254, 0.3);
    font-weight: bold;
  }

  .product-tile:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(162, 155, 254, 0.4);
  }

  .product-tile[style*="--tile-color"] {
    background: linear-gradient(135deg, var(--tile-color), color-mix(in srgb, var(--tile-color) 70%, black));
  }

  .product-tile.empty-tile {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    color: #64748b;
    border: 2px dashed #94a3b8;
  }

  .product-tile.empty-tile:hover {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #475569;
  }

  .tile-image {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .tile-name {
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.2;
  }

  .section-title {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: bold;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Products */
  .products-section {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    overflow-y: auto;
    flex: 1;
    padding-right: 0.5rem;
  }

  .product-card {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    border: none;
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 160px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(162, 155, 254, 0.3);
  }

  .product-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(162, 155, 254, 0.4);
  }

  .product-card[style*="--product-color"] {
    background: linear-gradient(135deg, var(--product-color), color-mix(in srgb, var(--product-color) 70%, black));
  }

  .product-info {
    flex: 1;
  }

  .product-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .product-description {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.3;
  }

  .product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: #fff;
    margin-bottom: 0.5rem;
  }

  .product-barcode {
    font-size: 0.8rem;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
  }

  .product-add {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.8;
    transition: all 0.3s ease;
  }

  .product-card:hover .product-add {
    opacity: 1;
    transform: scale(1.2);
  }

  /* Enhanced Cart Panel */
  .cart-panel {
    background: white;
    border-radius: 16px;
    padding: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 8rem);
  }

  /* Barcode Scanner in Cart Panel */
  .barcode-section {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border-radius: 12px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
  }

  .barcode-input-group {
    display: flex;
    gap: 0.5rem;
  }

  .barcode-input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f1f5f9;
  }

  .cart-count {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-weight: bold;
    font-size: 0.85rem;
  }

  .cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .cart-items::-webkit-scrollbar {
    width: 8px;
  }

  .cart-items::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  .cart-items::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }

  .cart-items::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .empty-cart {
    text-align: center;
    padding: 2rem 1rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-subtitle {
    font-size: 0.85rem;
    opacity: 0.7;
  }

  .cart-item {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 8px;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 3px solid var(--item-color, #3b82f6);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .cart-item:hover {
    transform: translateX(2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .cart-item.selected {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-left-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }

  .item-info {
    flex: 1;
  }

  .item-name {
    margin: 0 0 0.125rem 0;
    font-size: 0.9rem;
    font-weight: bold;
    color: #1e293b;
  }

  .item-details {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .item-price {
    font-weight: 600;
    color: #059669;
    font-size: 0.8rem;
  }

  .item-quantity {
    font-size: 0.8rem;
    color: #64748b;
  }

  .item-barcode {
    font-size: 0.75rem;
    color: #64748b;
    opacity: 0.8;
  }

  .quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    background: white;
    border-radius: 6px;
    padding: 0.2rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    margin-top: 0.25rem;
  }

  .qty-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 4px;
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
  }

  .qty-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(59, 130, 246, 0.4);
  }

  .item-quantity {
    font-weight: bold;
    color: #1e293b;
    min-width: 1.5rem;
    text-align: center;
    font-size: 0.9rem;
  }

  .item-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.4rem;
  }

  .item-total {
    font-size: 0.9rem;
    font-weight: bold;
    color: #059669;
  }

  /* Centralized Controls */
  .centralized-controls {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border-radius: 12px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border: 2px solid #e2e8f0;
  }

  .selected-item-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #d1d5db;
  }

  .selected-name {
    font-weight: bold;
    color: #1e293b;
    font-size: 0.9rem;
  }

  .selected-price {
    font-weight: 600;
    color: #059669;
    font-size: 0.85rem;
  }

  .quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
  }

  .qty-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 6px;
    color: white;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: bold;
  }

  .qty-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(59, 130, 246, 0.4);
  }

  .qty-input {
    width: 4rem;
    padding: 0.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
  }

  .qty-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .btn-remove-selected {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    border-radius: 6px;
    color: white;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    margin-left: 0.5rem;
  }

  .btn-remove-selected:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 10px rgba(239, 68, 68, 0.4);
  }

  .no-selection {
    text-align: center;
    color: #64748b;
    font-size: 0.85rem;
    padding: 0.5rem;
  }

  /* Streamlined Cart Summary */
  .cart-summary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1rem;
    border: 2px solid #e2e8f0;
  }

  .summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }

  .total-line {
    font-size: 1.1rem;
    font-weight: bold;
    color: #059669;
    border-top: 2px solid #d1d5db;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }

  /* Payment Actions */
  .payment-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .pay-btn {
    flex: 2;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .receipt-btn {
    flex: 1;
    padding: 1rem;
    font-size: 0.9rem;
    font-weight: bold;
  }

  /* Till Controls */
  .till-controls {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-top: 1px solid #e2e8f0;
  }

  .till-btn {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    font-weight: bold;
  }

  .payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .payment-btn {
    padding: 1rem;
    font-size: 1rem;
    font-weight: bold;
  }

  /* Buttons */
  .btn {
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  }

  .btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
  }

  .btn-accent {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  }

  .btn-accent:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  }

  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .new-product-modal,
  .tile-products-modal,
  .receipt-modal {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .modal-header {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
  }

  .btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
  }

  .btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  /* New Product Modal */
  .modal-content {
    padding: 2rem;
  }

  .barcode-info {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
  }

  .info-text {
    color: #64748b;
    margin-bottom: 1.5rem;
  }

  .vat-info {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-weight: 600;
  }

  /* Tile Products Modal */
  .tile-products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 2rem;
  }

  .tile-product-btn {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    border: none;
    border-radius: 12px;
    padding: 1.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 120px;
    box-shadow: 0 4px 15px rgba(162, 155, 254, 0.3);
  }

  .tile-product-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(162, 155, 254, 0.4);
  }

  .tile-product-btn[style*="--product-color"] {
    background: linear-gradient(135deg, var(--product-color), color-mix(in srgb, var(--product-color) 70%, black));
  }

  .tile-product-btn.add-product-btn {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    color: #64748b;
    border: 2px dashed #94a3b8;
  }

  .tile-product-btn.add-product-btn:hover {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #475569;
  }

  .product-name {
    font-weight: bold;
    font-size: 1rem;
    text-align: center;
  }

  .product-price {
    font-size: 1.1rem;
    font-weight: bold;
  }

  .product-barcode {
    font-size: 0.8rem;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
  }

  .add-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .add-text {
    font-size: 0.9rem;
    font-weight: bold;
  }

  /* Full-Screen Payment Interface */
  .fullscreen-payment {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 2000;
    display: flex;
    flex-direction: column;
  }

  .payment-header {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .payment-header h1 {
    margin: 0;
    font-size: 2rem;
  }

  .btn-close-payment {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem;
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.3s ease;
  }

  .btn-close-payment:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .payment-main {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    padding: 2rem;
    min-height: 0;
  }

  /* Payment Methods Panel */
  .payment-methods-panel {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
  }

  .payment-methods-panel h2 {
    margin: 0 0 2rem 0;
    font-size: 1.5rem;
    color: #1e293b;
  }

  .payment-method-tiles {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .payment-method-tile {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 3px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
  }

  .payment-method-tile:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }

  .payment-method-tile.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  }

  .payment-icon {
    font-size: 3rem;
  }

  .payment-name {
    font-size: 1.2rem;
    font-weight: bold;
  }

  /* Cash Payment Section */
  .cash-payment-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .amount-input {
    margin-bottom: 1rem;
  }

  .amount-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #374151;
  }

  .amount-display {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .currency {
    font-size: 1.5rem;
    font-weight: bold;
    color: #059669;
    padding: 0 0.5rem;
  }

  .amount-input-field {
    flex: 1;
    border: none;
    font-size: 1.5rem;
    font-weight: bold;
    padding: 0.5rem;
    outline: none;
    background: transparent;
  }

  .change-info {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
  }

  .complete-payment-btn {
    margin-top: auto;
    padding: 1.5rem;
    font-size: 1.2rem;
    font-weight: bold;
  }

  /* Payment Cart Panel */
  .payment-cart-panel {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
  }

  .payment-cart-panel h2 {
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    color: #1e293b;
  }

  .payment-cart-items {
    flex: 1;
    margin-bottom: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
  }

  .payment-cart-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .payment-cart-item:last-child {
    border-bottom: none;
  }

  .payment-cart-item .item-name {
    font-weight: 600;
    color: #1e293b;
  }

  .payment-cart-item .item-qty {
    color: #059669;
    font-weight: bold;
  }

  .payment-cart-item .item-total {
    font-weight: bold;
    color: #1e293b;
  }

  .payment-totals {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
  }

  .total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    font-size: 1rem;
  }

  .grand-total {
    font-size: 1.3rem;
    font-weight: bold;
    color: #059669;
    border-top: 2px solid #d1d5db;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
  }

  .payment-summary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
  }

  .total-due {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2rem;
    font-weight: bold;
  }

  .total-due .amount {
    color: #059669;
    font-size: 1.5rem;
  }

  .cash-input-section {
    margin-bottom: 2rem;
  }

  .cash-input-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #374151;
  }

  .cash-display {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .currency-symbol {
    font-size: 1.5rem;
    font-weight: bold;
    color: #059669;
    padding: 0 0.5rem;
  }

  .cash-input {
    flex: 1;
    border: none;
    font-size: 1.5rem;
    font-weight: bold;
    padding: 0.5rem;
    outline: none;
    background: transparent;
  }

  .denomination-buttons h3 {
    margin: 0 0 1rem 0;
    color: #374151;
  }

  .denomination-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 2rem;
  }

  .denomination-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .denomination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
  }

  .denomination-btn.exact-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    grid-column: span 3;
  }

  .number-pad {
    margin-bottom: 2rem;
  }

  .number-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .number-btn {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 1rem;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .number-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.4);
  }

  .number-btn.clear-btn {
    background: linear-gradient(135deg, #ef4444, #dc2626);
  }

  .change-display {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 1rem;
  }

  .change-amount {
    font-size: 1.5rem;
    font-weight: bold;
  }

  /* Manual Item Modal Styles */
  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #374151;
  }

  .form-input,
  .form-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
  }

  .form-input:focus,
  .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Receipt Modal Styles */
  .receipt-content {
    padding: 2rem;
  }

  .receipt-business {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px dashed #d1d5db;
  }

  .receipt-business h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #1e293b;
  }

  .receipt-business p {
    margin: 0.25rem 0;
    color: #64748b;
  }

  .receipt-transaction {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .receipt-transaction p {
    margin: 0.5rem 0;
    color: #374151;
  }

  .receipt-items {
    margin-bottom: 1.5rem;
  }

  .receipt-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .receipt-item-name {
    font-weight: 600;
    color: #1e293b;
  }

  .receipt-item-qty {
    color: #059669;
    font-weight: bold;
  }

  .receipt-item-price {
    font-weight: bold;
    color: #1e293b;
  }

  .receipt-totals {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .receipt-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .receipt-total {
    font-size: 1.2rem;
    border-top: 2px solid #d1d5db;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }

  .receipt-footer {
    text-align: center;
    color: #64748b;
    font-style: italic;
  }

  .modal-actions {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
  }

  .modal-actions .btn {
    flex: 1;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .pos-main {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .product-tiles-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .payment-main {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .payment-method-tiles {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .pos-header {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
    }

    .pos-title {
      font-size: 1.5rem;
    }

    .product-tiles-grid {
      grid-template-columns: repeat(2, 1fr);
      min-height: 400px;
    }

    .tile-products-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .payment-actions {
      flex-direction: column;
    }

    .number-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 480px) {
    .pos-main {
      padding: 0.5rem;
    }

    .product-tiles-grid {
      grid-template-columns: 1fr 1fr;
      gap: 0.5rem;
    }

    .cart-panel {
      padding: 0.75rem;
    }

    .barcode-section {
      padding: 0.75rem;
    }

    .tile-products-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
